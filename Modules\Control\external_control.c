/**
  ******************************************************************************
  * @file    external_control.c
  * <AUTHOR> 第三问 外部控制接口
  * @version V1.0
  * @date    2024
  * @brief   外部控制接口实现 (预留扩展)
  ******************************************************************************
  * @attention
  * 
  * 本文件为外部控制接口预留文件
  * 可根据实际需求添加各种外部控制功能
  * 
  ******************************************************************************
  */

/* Includes ------------------------------------------------------------------*/
#include "external_control.h"
#include "../Generation/ad9854.h"
#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include <math.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

// 外部控制相关变量预留

/* Private function prototypes -----------------------------------------------*/

/* Exported functions --------------------------------------------------------*/

// ==================== 外部控制函数实现 ====================

/**
 * @brief  外部控制主循环处理
 * @param  None
 * @retval None
 */
void ExternalControl_Process(void)
{
    // 外部控制处理预留
    // 可在此处添加其他外部控制接口
}

/************************ (C) COPYRIGHT STMicroelectronics *****END OF FILE****/
