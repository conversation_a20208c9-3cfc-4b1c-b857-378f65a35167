/**
  ******************************************************************************
  * @file    signal_generator.c
  * <AUTHOR> - AD9854版本
  * @version V1.1
  * @date    2024
  * @brief   信号发生器控制模块实现 - 适配AD9854
  ******************************************************************************
  */

#include "signal_generator.h"
#include "../Core/systick.h"
#include <string.h>
#include <stdio.h>

/* Private typedef -----------------------------------------------------------*/

/* Private define ------------------------------------------------------------*/

#define DISPLAY_LINE_FREQ           0
#define DISPLAY_LINE_AMP            1
#define DISPLAY_LINE_WAVE           2
#define DISPLAY_LINE_MODE           3

/* Private macro -------------------------------------------------------------*/

/* Private variables ---------------------------------------------------------*/

/**
  * @brief  初始化标志
  */
volatile bool g_signal_generator_initialized = false;

/**
  * @brief  信号发生器参数
  */
static Signal_Generator_t s_sig_gen = {
    .frequency_hz = SIG_GEN_FREQ_DEFAULT_HZ,
    .amplitude_mv = SIG_GEN_AMP_DEFAULT_MV,
    .wave_type = WAVE_SINE,
    .work_mode = MODE_NORMAL,
    .edit_state = EDIT_STATE_NONE,
    .edit_position = 0,
    .buffer_index = 0,
    .parameter_changed = false,
    .display_update_needed = true
};

/**
  * @brief  测量结果
  */
static Measurement_Result_t s_measurement = {
    .input_freq_hz = 0,
    .input_amp_mv = 0,
    .phase_deg = 0.0f,
    .gain_db = 0.0f,
    .valid = false
};

/**
  * @brief  波形名称表
  */
static const char* s_wave_names[WAVE_COUNT] = {
    "SINE", "SQUARE", "TRIANGLE", "SAWTOOTH"
};

/**
  * @brief  模式名称表
  */
static const char* s_mode_names[MODE_COUNT] = {
    "NORMAL", "FREQ_SET", "AMP_SET", "WAVE_SEL", "MEASURE"
};

/* Private function prototypes -----------------------------------------------*/

static void SignalGenerator_ProcessKeyboard(void);
static void SignalGenerator_ProcessNormalMode(Key_Event_t* event);
static void SignalGenerator_ProcessFreqSetMode(Key_Event_t* event);
static void SignalGenerator_ProcessAmpSetMode(Key_Event_t* event);
static void SignalGenerator_ProcessWaveSelMode(Key_Event_t* event);
static void SignalGenerator_UpdateParameters(void);
static void SignalGenerator_UpdateDisplayContent(void);
static bool SignalGenerator_ApplyFrequency(uint32_t freq_hz);
static bool SignalGenerator_ApplyAmplitude(uint16_t amp_mv);
static bool SignalGenerator_ApplyWaveType(Wave_Type_t wave_type);

/* Exported functions --------------------------------------------------------*/

/**
  * @brief  信号发生器初始化
  * @param  None
  * @retval 0: 成功, -1: 失败
  */
int8_t SignalGenerator_Init(void)
{
    // 初始化AD9854控制模块
    if (AD9854_Control_Init() != CONTROL_STATUS_OK) {
        return -1;
    }
    
    // 初始化OLED显示
    if (OLED_Init() != 0) {
        return -1;
    }
    
    // 初始化矩阵键盘
    if (MatrixKeypad_Init() != 0) {
        return -1;
    }
    
    // 设置默认参数
    s_sig_gen.frequency_hz = SIG_GEN_FREQ_DEFAULT_HZ;
    s_sig_gen.amplitude_mv = SIG_GEN_AMP_DEFAULT_MV;
    s_sig_gen.wave_type = WAVE_SINE;
    s_sig_gen.work_mode = MODE_NORMAL;
    s_sig_gen.display_update_needed = true;
    
    // 应用默认参数到AD9854
    SignalGenerator_ApplyFrequency(s_sig_gen.frequency_hz);
    SignalGenerator_ApplyAmplitude(s_sig_gen.amplitude_mv);
    SignalGenerator_ApplyWaveType(s_sig_gen.wave_type);
    
    // 使能输出
    AD9854_Control_EnableOutput(true);
    
    g_signal_generator_initialized = true;
    
    return 0;
}

/**
  * @brief  信号发生器反初始化
  * @param  None
  * @retval None
  */
void SignalGenerator_DeInit(void)
{
    // 禁用输出
    AD9854_Control_EnableOutput(false);
    
    // 反初始化各模块
    AD9854_Control_DeInit();
    MatrixKeypad_DeInit();
    
    g_signal_generator_initialized = false;
}

/**
  * @brief  信号发生器主循环处理
  * @param  None
  * @retval None
  */
void SignalGenerator_Process(void)
{
    if (!g_signal_generator_initialized) {
        return;
    }
    
    // 处理键盘输入
    SignalGenerator_ProcessKeyboard();
    
    // 更新参数
    if (s_sig_gen.parameter_changed) {
        SignalGenerator_UpdateParameters();
        s_sig_gen.parameter_changed = false;
    }
    
    // 更新显示
    if (s_sig_gen.display_update_needed) {
        SignalGenerator_UpdateDisplayContent();
        s_sig_gen.display_update_needed = false;
    }
}

/**
  * @brief  设置频率
  * @param  frequency_hz: 频率值 (Hz)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetFrequency(uint32_t frequency_hz)
{
    if (!SIG_GEN_IS_FREQ_VALID(frequency_hz)) {
        return false;
    }
    
    s_sig_gen.frequency_hz = frequency_hz;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return SignalGenerator_ApplyFrequency(frequency_hz);
}

/**
  * @brief  设置幅度
  * @param  amplitude_mv: 幅度值 (mV)
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetAmplitude(uint16_t amplitude_mv)
{
    if (!SIG_GEN_IS_AMP_VALID(amplitude_mv)) {
        return false;
    }
    
    s_sig_gen.amplitude_mv = amplitude_mv;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return SignalGenerator_ApplyAmplitude(amplitude_mv);
}

/**
  * @brief  设置波形类型
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_SetWaveType(Wave_Type_t wave_type)
{
    if (wave_type >= WAVE_COUNT) {
        return false;
    }
    
    s_sig_gen.wave_type = wave_type;
    s_sig_gen.parameter_changed = true;
    s_sig_gen.display_update_needed = true;
    
    return SignalGenerator_ApplyWaveType(wave_type);
}

/**
  * @brief  获取当前参数
  * @param  None
  * @retval 信号发生器参数结构体指针
  */
const Signal_Generator_t* SignalGenerator_GetParams(void)
{
    return &s_sig_gen;
}

/**
  * @brief  开始测量模式
  * @param  None
  * @retval true: 成功, false: 失败
  */
bool SignalGenerator_StartMeasurement(void)
{
    s_sig_gen.work_mode = MODE_MEASURE;
    s_sig_gen.display_update_needed = true;
    
    // 这里可以添加测量逻辑
    // 暂时返回模拟数据
    s_measurement.valid = false;
    
    return true;
}

/**
  * @brief  获取测量结果
  * @param  None
  * @retval 测量结果结构体指针
  */
const Measurement_Result_t* SignalGenerator_GetMeasurement(void)
{
    return &s_measurement;
}

/**
  * @brief  强制更新显示
  * @param  None
  * @retval None
  */
void SignalGenerator_UpdateDisplay(void)
{
    s_sig_gen.display_update_needed = true;
}

/**
  * @brief  检查是否就绪
  * @param  None
  * @retval true: 就绪, false: 未就绪
  */
bool SignalGenerator_IsReady(void)
{
    return g_signal_generator_initialized && AD9854_Control_IsReady();
}

/* Private functions ---------------------------------------------------------*/

/**
  * @brief  处理键盘输入
  * @param  None
  * @retval None
  */
static void SignalGenerator_ProcessKeyboard(void)
{
    Key_Event_t event;
    
    if (MatrixKeypad_ScanAll(&event)) {
        // 根据当前模式处理按键
        switch (s_sig_gen.work_mode) {
            case MODE_NORMAL:
                SignalGenerator_ProcessNormalMode(&event);
                break;
            case MODE_FREQ_SET:
                SignalGenerator_ProcessFreqSetMode(&event);
                break;
            case MODE_AMP_SET:
                SignalGenerator_ProcessAmpSetMode(&event);
                break;
            case MODE_WAVE_SEL:
                SignalGenerator_ProcessWaveSelMode(&event);
                break;
            default:
                break;
        }
        
        s_sig_gen.display_update_needed = true;
    }
}

/**
  * @brief  处理正常模式按键
  * @param  event: 按键事件
  * @retval None
  */
static void SignalGenerator_ProcessNormalMode(Key_Event_t* event)
{
    if (event->keypad_id == KEYPAD_1) {
        // 第一块键盘 - 数字和快捷调整
        switch (event->key_code) {
            case KEY1_FREQ_UP:
                SignalGenerator_SetFrequency(s_sig_gen.frequency_hz + SIG_GEN_FREQ_STEP_HZ);
                break;
            case KEY1_FREQ_DOWN:
                if (s_sig_gen.frequency_hz > SIG_GEN_FREQ_STEP_HZ) {
                    SignalGenerator_SetFrequency(s_sig_gen.frequency_hz - SIG_GEN_FREQ_STEP_HZ);
                }
                break;
            case KEY1_AMP_UP:
                SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv + SIG_GEN_AMP_STEP_MV);
                break;
            case KEY1_AMP_DOWN:
                if (s_sig_gen.amplitude_mv > SIG_GEN_AMP_STEP_MV) {
                    SignalGenerator_SetAmplitude(s_sig_gen.amplitude_mv - SIG_GEN_AMP_STEP_MV);
                }
                break;
        }
    } else if (event->keypad_id == KEYPAD_2) {
        // 第二块键盘 - 功能控制
        switch (event->key_code) {
            case KEY2_FREQ_SET:
                s_sig_gen.work_mode = MODE_FREQ_SET;
                break;
            case KEY2_AMP_SET:
                s_sig_gen.work_mode = MODE_AMP_SET;
                break;
            case KEY2_SIN:
                SignalGenerator_SetWaveType(WAVE_SINE);
                break;
            case KEY2_SQUARE:
                SignalGenerator_SetWaveType(WAVE_SQUARE);
                break;
            case KEY2_TRIANGLE:
                SignalGenerator_SetWaveType(WAVE_TRIANGLE);
                break;
            case KEY2_SAWTOOTH:
                SignalGenerator_SetWaveType(WAVE_SAWTOOTH);
                break;
        }
    }
}

/**
  * @brief  处理频率设置模式按键
  * @param  event: 按键事件
  * @retval None
  */
static void SignalGenerator_ProcessFreqSetMode(Key_Event_t* event)
{
    // 简化实现：直接返回正常模式
    s_sig_gen.work_mode = MODE_NORMAL;
}

/**
  * @brief  处理幅度设置模式按键
  * @param  event: 按键事件
  * @retval None
  */
static void SignalGenerator_ProcessAmpSetMode(Key_Event_t* event)
{
    // 简化实现：直接返回正常模式
    s_sig_gen.work_mode = MODE_NORMAL;
}

/**
  * @brief  处理波形选择模式按键
  * @param  event: 按键事件
  * @retval None
  */
static void SignalGenerator_ProcessWaveSelMode(Key_Event_t* event)
{
    // 简化实现：直接返回正常模式
    s_sig_gen.work_mode = MODE_NORMAL;
}

/**
  * @brief  更新参数到硬件
  * @param  None
  * @retval None
  */
static void SignalGenerator_UpdateParameters(void)
{
    // 参数已在设置函数中直接应用到硬件
}

/**
  * @brief  更新显示内容
  * @param  None
  * @retval None
  */
static void SignalGenerator_UpdateDisplayContent(void)
{
    char line_buffer[32];
    
    // 清屏
    OLED_Clear();
    
    // 显示频率
    snprintf(line_buffer, sizeof(line_buffer), "F:%lu Hz", s_sig_gen.frequency_hz);
    OLED_ShowString(0, DISPLAY_LINE_FREQ * 16, line_buffer, 12);
    
    // 显示幅度
    snprintf(line_buffer, sizeof(line_buffer), "A:%u mV", s_sig_gen.amplitude_mv);
    OLED_ShowString(0, DISPLAY_LINE_AMP * 16, line_buffer, 12);
    
    // 显示波形
    snprintf(line_buffer, sizeof(line_buffer), "W:%s", s_wave_names[s_sig_gen.wave_type]);
    OLED_ShowString(0, DISPLAY_LINE_WAVE * 16, line_buffer, 12);
    
    // 显示模式
    snprintf(line_buffer, sizeof(line_buffer), "M:%s", s_mode_names[s_sig_gen.work_mode]);
    OLED_ShowString(0, DISPLAY_LINE_MODE * 16, line_buffer, 12);
    
    // 刷新显示
    OLED_Refresh();
}

/**
  * @brief  应用频率到硬件
  * @param  freq_hz: 频率值
  * @retval true: 成功, false: 失败
  */
static bool SignalGenerator_ApplyFrequency(uint32_t freq_hz)
{
    return (AD9854_Control_SetFrequency(freq_hz) == CONTROL_STATUS_OK);
}

/**
  * @brief  应用幅度到硬件
  * @param  amp_mv: 幅度值
  * @retval true: 成功, false: 失败
  */
static bool SignalGenerator_ApplyAmplitude(uint16_t amp_mv)
{
    return (AD9854_Control_SetTargetAmplitude(amp_mv) == CONTROL_STATUS_OK);
}

/**
  * @brief  应用波形类型到硬件
  * @param  wave_type: 波形类型
  * @retval true: 成功, false: 失败
  */
static bool SignalGenerator_ApplyWaveType(Wave_Type_t wave_type)
{
    AD9854_Wave_t ad9854_wave = (AD9854_Wave_t)wave_type;
    return (AD9854_Control_SetWaveType(ad9854_wave) == CONTROL_STATUS_OK);
}

/************************ (C) COPYRIGHT 嵌入式竞赛团队 *****END OF FILE****/
