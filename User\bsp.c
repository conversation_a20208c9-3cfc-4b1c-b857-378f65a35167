#include "bsp.h"

/**
 * @brief  板级支持包初始化函数
 * @param  None
 * @retval None
 * @note   完成系统时钟和基础外设的初始化
 */
void BSP_Init(void)
{
    // 1. 使能所有GPIO端口时钟 (为矩阵键盘和AD9854)
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOA, ENABLE);  // 矩阵键盘1
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOB, ENABLE);  // OLED + AD9854部分引脚
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOC, ENABLE);  // 矩阵键盘2
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOD, ENABLE);  // AD9854数据总线
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_GPIOE, ENABLE);  // AD9854控制+地址总线

    // 2. 使能必要的外设时钟
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_I2C1, ENABLE);   // OLED显示
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_SPI1, ENABLE);   // 预留SPI接口
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_SPI2, ENABLE);   // 预留SPI接口
    RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2, ENABLE);   // 定时器
    RCC_APB2PeriphClockCmd(RCC_APB2Periph_ADC1, ENABLE);   // 预留ADC
    RCC_AHB1PeriphClockCmd(RCC_AHB1Periph_DMA2, ENABLE);   // 预留DMA
}

// SysTick_Handler函数已在stm32f4xx_it.c中定义，此处不重复定义
